# API Test Documentation

## Base URL
`http://localhost:5038`

## Authentication & Authorization

### POST /api/auth
Login using JWT (email + password)

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "123456"
}
```

**Response (Success):**
```json
{
  "token": "<JWT token>",
  "role": "administrator"
}
```

**Response (Error):**
```json
{
  "errorCode": "HB40101",
  "message": "Invalid email or password"
}
```

## Handbag API Endpoints

### GET /api/handbags
List all handbags with brand info
- **Roles:** administrator, moderator, developer, member
- **Status:** 200, 401, 403

### GET /api/handbags/{id}
Get handbag by ID
- **Roles:** administrator, moderator, developer, member
- **Status:** 200, 404, 401, 403

### POST /api/handbags
Create a new handbag
- **Roles:** administrator, moderator
- **Status:** 201, 400, 401, 403

**Request Body:**
```json
{
  "modelName": "Elegant #2024",
  "material": "Leather",
  "price": 250.5,
  "stock": 10,
  "brandId": 1
}
```

**Validation Rules:**
- modelName: Regex `^([A-Z0-9][a-zA-Z0-9#]*\s)*([A-Z0-9][a-zA-Z0-9#]*)$`
- price, stock > 0

### PUT /api/handbags/{id}
Update an existing handbag
- **Roles:** administrator, moderator
- **Status:** 200, 400, 404, 401, 403

### DELETE /api/handbags/{id}
Delete a handbag
- **Roles:** administrator, moderator
- **Status:** 200, 404, 401, 403

### GET /api/handbags/search?modelName=...&material=...
Search handbags by modelName and material (OData supported)
- Results grouped by brand name
- **Roles:** all roles with token
- **Status:** 200, 401, 403

## Error Code Format

All errors return JSON in this format:
```json
{
  "errorCode": "HB40001",
  "message": "modelName is required"
}
```

| errorCode | HTTP Status | Meaning |
|-----------|-------------|---------|
| HB40001   | 400         | Missing/invalid input |
| HB40101   | 401         | Token missing/invalid |
| HB40301   | 403         | Permission denied |
| HB40401   | 404         | Resource not found |
| HB50001   | 500         | Internal server error |

## Role Mapping
- 1 = administrator (Full access)
- 2 = moderator (Full access)
- 3 = developer (Read + search only)
- 4 = member (Read + search only)
- Other roles: No token issued

## Test Examples

### 1. Login Test
```bash
curl -X POST http://localhost:5038/api/auth \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "123456"}'
```

### 2. Get All Handbags (with token)
```bash
curl -X GET http://localhost:5038/api/handbags \
  -H "Authorization: Bearer <your-jwt-token>"
```

### 3. Create Handbag (admin/moderator only)
```bash
curl -X POST http://localhost:5038/api/handbags \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your-jwt-token>" \
  -d '{
    "modelName": "Elegant #2024",
    "material": "Leather",
    "price": 250.5,
    "stock": 10,
    "brandId": 1
  }'
```

### 4. Search Handbags (grouped by brand)
```bash
curl -X GET "http://localhost:5038/api/handbags/search?modelName=Elegant&material=Leather" \
  -H "Authorization: Bearer <your-jwt-token>"
```

### 5. Test Invalid ModelName (should fail validation)
```bash
curl -X POST http://localhost:5038/api/handbags \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your-jwt-token>" \
  -d '{
    "modelName": "invalid-name",
    "material": "Leather",
    "price": 250.5,
    "stock": 10,
    "brandId": 1
  }'
```

Expected response:
```json
{
  "errorCode": "HB40001",
  "message": "modelName format is invalid"
}
```
