# Handbag API Test Guide

## Tổng quan
File này chứa hướng dẫn chi tiết để test tất cả các API endpoints của Handbag API theo yêu cầu.

## Cách sử dụng file test

### 1. Khởi động server
```bash
cd PRN231_SU25_SE170115.api
dotnet run
```
Server sẽ chạy tại: `http://localhost:5038`

### 2. Sử dụng file test HTTP
- Mở file `PRN231_SU25_SE170115.api.http` trong VS Code
- Cài đặt extension "REST Client" nếu chưa có
- Click vào "Send Request" để thực hiện từng test

### 3. Quy trình test đầy đủ

#### Bước 1: Test Authentication
1. **Test 1-5**: Login với các role khác nhau
   - Admin: `<EMAIL> / 123456`
   - Moderator: `<EMAIL> / 123456`
   - Developer: `<EMAIL> / 123456`
   - Member: `<EMAIL> / 123456`
   - Invalid credentials (test lỗi 401)

2. **Lưu JWT tokens**: Copy token từ response và thay thế `<JWT_TOKEN>` trong các test tiếp theo

#### Bước 2: Test GET Endpoints
3. **Test 6-9**: Test GET operations
   - GET all handbags (với và không có token)
   - GET handbag by ID (valid và invalid ID)
   - Kiểm tra status codes: 200, 401, 403, 404

#### Bước 3: Test POST Endpoints (Create)
4. **Test 10-14**: Test tạo handbag mới
   - Valid data (admin/moderator)
   - Invalid modelName (regex validation)
   - Invalid price (< 0)
   - Invalid stock (≤ 0)
   - Unauthorized role (developer/member)

#### Bước 4: Test PUT Endpoints (Update)
5. **Test 15-18**: Test cập nhật handbag
   - Valid update (admin/moderator)
   - Update non-existing ID (404)
   - Invalid data (400)
   - Unauthorized role (403)

#### Bước 5: Test DELETE Endpoints
6. **Test 19-21**: Test xóa handbag
   - Valid delete (admin/moderator)
   - Delete non-existing ID (404)
   - Unauthorized role (403)

#### Bước 6: Test SEARCH Endpoints
7. **Test 22-26**: Test search functionality
   - Search by modelName
   - Search by material
   - Search by both parameters
   - Search without token (401)
   - Search with different roles

#### Bước 7: Test Validation Rules
8. **Test 27-30**: Test chi tiết validation
   - Valid modelName patterns
   - Invalid modelName patterns
   - Missing required fields

#### Bước 8: Test Role-based Access
9. **Test 31-33**: Test quyền truy cập theo role
   - Member/Developer: chỉ read + search
   - Admin/Moderator: full access

## Expected Results

### Status Codes
- **200**: Success (GET, PUT, DELETE)
- **201**: Created (POST)
- **400**: Bad Request (validation errors)
- **401**: Unauthorized (no token/invalid token)
- **403**: Forbidden (insufficient permissions)
- **404**: Not Found (resource doesn't exist)

### Error Response Format
```json
{
  "errorCode": "HB40001",
  "message": "Detailed error message"
}
```

### Success Response Examples

#### Login Success
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "role": "administrator"
}
```

#### Get Handbags Success
```json
[
  {
    "id": 1,
    "modelName": "Elegant #2024",
    "material": "Leather",
    "price": 250.5,
    "stock": 10,
    "brandId": 1,
    "brand": {
      "id": 1,
      "name": "Luxury Brand"
    }
  }
]
```

#### Search Results (Grouped by Brand)
```json
{
  "Luxury Brand": [
    {
      "id": 1,
      "modelName": "Elegant #2024",
      "material": "Leather",
      "price": 250.5,
      "stock": 10
    }
  ],
  "Premium Brand": [
    {
      "id": 2,
      "modelName": "Classic #2024",
      "material": "Leather",
      "price": 300.0,
      "stock": 5
    }
  ]
}
```

## Validation Rules Detail

### ModelName Regex
Pattern: `^([A-Z0-9][a-zA-Z0-9#]*\s)*([A-Z0-9][a-zA-Z0-9#]*)$`

**Valid examples:**
- `A1`
- `Model #123`
- `Elegant #2024`
- `Test Bag #1`

**Invalid examples:**
- `invalid-name` (starts with lowercase)
- `model name` (starts with lowercase)
- `-Invalid` (starts with special char)
- `Test-Name` (contains hyphen)

### Price & Stock Rules
- `price > 0`
- `stock > 0`

## Role Permissions

| Role | GET | POST | PUT | DELETE | SEARCH |
|------|-----|------|-----|--------|--------|
| Administrator | ✅ | ✅ | ✅ | ✅ | ✅ |
| Moderator | ✅ | ✅ | ✅ | ✅ | ✅ |
| Developer | ✅ | ❌ | ❌ | ❌ | ✅ |
| Member | ✅ | ❌ | ❌ | ❌ | ✅ |

## Test Data Setup
Đảm bảo database có sẵn:
- Brands với ID 1, 2
- Handbags với ID 1, 2 để test
- Users với các role khác nhau

## Troubleshooting
1. **Server không khởi động**: Kiểm tra port 5038 có bị chiếm không
2. **401 Unauthorized**: Kiểm tra JWT token có hợp lệ không
3. **404 Not Found**: Kiểm tra ID có tồn tại trong database không
4. **400 Bad Request**: Kiểm tra format dữ liệu và validation rules
