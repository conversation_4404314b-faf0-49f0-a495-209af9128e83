using Models;
using System.Net;
using System.Text.Json;

namespace Middleware
{
    public class ExceptionMiddleware
    {
        private readonly RequestDelegate _next;

        public ExceptionMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                await HandleExceptionAsync(context, ex);
            }
        }

        private static Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            HttpStatusCode statusCode = exception switch
            {
                BadRequestException => HttpStatusCode.BadRequest,
                UnauthorizedTokenException => HttpStatusCode.Unauthorized,
                ForbiddenAccessException => HttpStatusCode.Forbidden,
                NotFoundException => HttpStatusCode.NotFound,
                _ => HttpStatusCode.InternalServerError
            };

            context.Response.ContentType = "application/json";
            context.Response.StatusCode = (int)statusCode;

            var (errorCode, message) = ErrorMap.GetError(statusCode);
            var result = JsonSerializer.Serialize(new
            {
                errorCode,
                message
            });

            return context.Response.WriteAsync(result);
        }
    }

    public class BadRequestException : Exception { }
    public class UnauthorizedTokenException : Exception { }
    public class ForbiddenAccessException : Exception { }
    public class NotFoundException : Exception { }

}
