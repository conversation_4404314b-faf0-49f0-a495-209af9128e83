# Handbag API Test Script
# <PERSON><PERSON><PERSON> tất cả các test cases cho Handbag API

param(
    [string]$BaseUrl = "http://localhost:5038",
    [switch]$Verbose
)

Write-Host "=== HANDBAG API TEST SCRIPT ===" -ForegroundColor Green
Write-Host "Base URL: $BaseUrl" -ForegroundColor Yellow

# Function để gọi API và hiển thị kết quả
function Invoke-ApiTest {
    param(
        [string]$Method,
        [string]$Url,
        [hashtable]$Headers = @{},
        [string]$Body = $null,
        [string]$TestName,
        [int[]]$ExpectedStatusCodes = @(200)
    )
    
    Write-Host "`n--- $TestName ---" -ForegroundColor Cyan
    
    try {
        $params = @{
            Method = $Method
            Uri = $Url
            Headers = $Headers
        }
        
        if ($Body) {
            $params.Body = $Body
            $params.ContentType = "application/json"
        }
        
        $response = Invoke-RestMethod @params -StatusCodeVariable statusCode
        
        if ($statusCode -in $ExpectedStatusCodes) {
            Write-Host "✅ PASS - Status: $statusCode" -ForegroundColor Green
        } else {
            Write-Host "❌ FAIL - Status: $statusCode (Expected: $($ExpectedStatusCodes -join ', '))" -ForegroundColor Red
        }
        
        if ($Verbose) {
            Write-Host "Response: $($response | ConvertTo-Json -Depth 3)" -ForegroundColor Gray
        }
        
        return @{
            Success = $statusCode -in $ExpectedStatusCodes
            StatusCode = $statusCode
            Response = $response
        }
    }
    catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        if ($statusCode -in $ExpectedStatusCodes) {
            Write-Host "✅ PASS - Status: $statusCode (Expected error)" -ForegroundColor Green
            return @{ Success = $true; StatusCode = $statusCode }
        } else {
            Write-Host "❌ FAIL - Error: $($_.Exception.Message)" -ForegroundColor Red
            return @{ Success = $false; StatusCode = $statusCode }
        }
    }
}

# Variables để lưu tokens
$adminToken = ""
$moderatorToken = ""
$developerToken = ""
$memberToken = ""

# Test counters
$totalTests = 0
$passedTests = 0

Write-Host "`n=== AUTHENTICATION TESTS ===" -ForegroundColor Magenta

# Test 1: Login Admin
$totalTests++
$result = Invoke-ApiTest -Method "POST" -Url "$BaseUrl/api/auth" -TestName "Login Admin" -ExpectedStatusCodes @(200) -Body @"
{
  "email": "<EMAIL>",
  "password": "123456"
}
"@
if ($result.Success) { 
    $passedTests++
    $adminToken = $result.Response.token
}

# Test 2: Login Invalid
$totalTests++
$result = Invoke-ApiTest -Method "POST" -Url "$BaseUrl/api/auth" -TestName "Login Invalid Credentials" -ExpectedStatusCodes @(401) -Body @"
{
  "email": "<EMAIL>",
  "password": "wrongpassword"
}
"@
if ($result.Success) { $passedTests++ }

# Test 3: Login Moderator
$totalTests++
$result = Invoke-ApiTest -Method "POST" -Url "$BaseUrl/api/auth" -TestName "Login Moderator" -ExpectedStatusCodes @(200) -Body @"
{
  "email": "<EMAIL>",
  "password": "123456"
}
"@
if ($result.Success) { 
    $passedTests++
    $moderatorToken = $result.Response.token
}

# Test 4: Login Developer
$totalTests++
$result = Invoke-ApiTest -Method "POST" -Url "$BaseUrl/api/auth" -TestName "Login Developer" -ExpectedStatusCodes @(200) -Body @"
{
  "email": "<EMAIL>",
  "password": "123456"
}
"@
if ($result.Success) { 
    $passedTests++
    $developerToken = $result.Response.token
}

# Test 5: Login Member
$totalTests++
$result = Invoke-ApiTest -Method "POST" -Url "$BaseUrl/api/auth" -TestName "Login Member" -ExpectedStatusCodes @(200) -Body @"
{
  "email": "<EMAIL>",
  "password": "123456"
}
"@
if ($result.Success) { 
    $passedTests++
    $memberToken = $result.Response.token
}

Write-Host "`n=== HANDBAG GET TESTS ===" -ForegroundColor Magenta

# Test 6: Get All Handbags (with token)
$totalTests++
$headers = @{ "Authorization" = "Bearer $adminToken" }
$result = Invoke-ApiTest -Method "GET" -Url "$BaseUrl/api/handbags" -Headers $headers -TestName "Get All Handbags (Admin)" -ExpectedStatusCodes @(200)
if ($result.Success) { $passedTests++ }

# Test 7: Get All Handbags (without token)
$totalTests++
$result = Invoke-ApiTest -Method "GET" -Url "$BaseUrl/api/handbags" -TestName "Get All Handbags (No Token)" -ExpectedStatusCodes @(401)
if ($result.Success) { $passedTests++ }

# Test 8: Get Handbag by ID (valid)
$totalTests++
$headers = @{ "Authorization" = "Bearer $adminToken" }
$result = Invoke-ApiTest -Method "GET" -Url "$BaseUrl/api/handbags/1" -Headers $headers -TestName "Get Handbag by Valid ID" -ExpectedStatusCodes @(200)
if ($result.Success) { $passedTests++ }

# Test 9: Get Handbag by ID (invalid)
$totalTests++
$headers = @{ "Authorization" = "Bearer $adminToken" }
$result = Invoke-ApiTest -Method "GET" -Url "$BaseUrl/api/handbags/999" -Headers $headers -TestName "Get Handbag by Invalid ID" -ExpectedStatusCodes @(404)
if ($result.Success) { $passedTests++ }

Write-Host "`n=== HANDBAG POST TESTS ===" -ForegroundColor Magenta

# Test 10: Create Handbag (valid data)
$totalTests++
$headers = @{ "Authorization" = "Bearer $adminToken" }
$result = Invoke-ApiTest -Method "POST" -Url "$BaseUrl/api/handbags" -Headers $headers -TestName "Create Handbag (Valid Data)" -ExpectedStatusCodes @(201) -Body @"
{
  "modelName": "Test #2024",
  "material": "Leather",
  "price": 250.5,
  "stock": 10,
  "brandId": 1
}
"@
if ($result.Success) { $passedTests++ }

# Test 11: Create Handbag (invalid modelName)
$totalTests++
$headers = @{ "Authorization" = "Bearer $adminToken" }
$result = Invoke-ApiTest -Method "POST" -Url "$BaseUrl/api/handbags" -Headers $headers -TestName "Create Handbag (Invalid ModelName)" -ExpectedStatusCodes @(400) -Body @"
{
  "modelName": "invalid-name",
  "material": "Leather",
  "price": 250.5,
  "stock": 10,
  "brandId": 1
}
"@
if ($result.Success) { $passedTests++ }

# Test 12: Create Handbag (developer role - should fail)
$totalTests++
$headers = @{ "Authorization" = "Bearer $developerToken" }
$result = Invoke-ApiTest -Method "POST" -Url "$BaseUrl/api/handbags" -Headers $headers -TestName "Create Handbag (Developer Role)" -ExpectedStatusCodes @(403) -Body @"
{
  "modelName": "Test #Bag",
  "material": "Leather",
  "price": 250.5,
  "stock": 10,
  "brandId": 1
}
"@
if ($result.Success) { $passedTests++ }

Write-Host "`n=== HANDBAG SEARCH TESTS ===" -ForegroundColor Magenta

# Test 13: Search with token
$totalTests++
$headers = @{ "Authorization" = "Bearer $adminToken" }
$result = Invoke-ApiTest -Method "GET" -Url "$BaseUrl/api/handbags/search?modelName=Elegant" -Headers $headers -TestName "Search Handbags (With Token)" -ExpectedStatusCodes @(200)
if ($result.Success) { $passedTests++ }

# Test 14: Search without token
$totalTests++
$result = Invoke-ApiTest -Method "GET" -Url "$BaseUrl/api/handbags/search?modelName=Elegant" -TestName "Search Handbags (No Token)" -ExpectedStatusCodes @(401)
if ($result.Success) { $passedTests++ }

# Test 15: Search with member token (should work)
$totalTests++
$headers = @{ "Authorization" = "Bearer $memberToken" }
$result = Invoke-ApiTest -Method "GET" -Url "$BaseUrl/api/handbags/search?material=Leather" -Headers $headers -TestName "Search Handbags (Member Role)" -ExpectedStatusCodes @(200)
if ($result.Success) { $passedTests++ }

Write-Host "`n=== TEST SUMMARY ===" -ForegroundColor Green
Write-Host "Total Tests: $totalTests" -ForegroundColor White
Write-Host "Passed: $passedTests" -ForegroundColor Green
Write-Host "Failed: $($totalTests - $passedTests)" -ForegroundColor Red
Write-Host "Success Rate: $([math]::Round(($passedTests / $totalTests) * 100, 2))%" -ForegroundColor Yellow

if ($passedTests -eq $totalTests) {
    Write-Host "`n🎉 ALL TESTS PASSED!" -ForegroundColor Green
} else {
    Write-Host "`n⚠️  SOME TESTS FAILED - Check the results above" -ForegroundColor Yellow
}
