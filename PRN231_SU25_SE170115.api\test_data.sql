-- Test data for Summer2025HandbagDB

-- Insert test users with different roles
INSERT INTO SystemAccounts (AccountID, Username, Email, Password, Role, IsActive) VALUES
(1, 'admin', '<EMAIL>', '123456', 1, 1),
(2, 'moderator', '<EMAIL>', '123456', 2, 1),
(3, 'developer', '<EMAIL>', '123456', 3, 1),
(4, 'member', '<EMAIL>', '123456', 4, 1),
(5, 'guest', '<EMAIL>', '123456', 5, 1);

-- Insert test brands
INSERT INTO Brand (BrandID, BrandName, Country, FoundedYear, Website) VALUES
(1, '<PERSON>', 'France', 1854, 'https://www.louisvuitton.com'),
(2, 'Gucci', 'Italy', 1921, 'https://www.gucci.com'),
(3, '<PERSON><PERSON>', 'France', 1910, 'https://www.chanel.com'),
(4, 'Her<PERSON><PERSON>', 'France', 1837, 'https://www.hermes.com'),
(5, 'Prada', 'Italy', 1913, 'https://www.prada.com');

-- Insert test handbags
INSERT INTO Handbag (HandbagID, BrandID, ModelName, Material, Color, Price, Stock, ReleaseDate) VALUES
(1, 1, 'Neverfull MM', 'Canvas', 'Brown', 1500.00, 10, '2024-01-15'),
(2, 1, 'Speedy 30', 'Leather', 'Black', 1200.00, 5, '2024-02-01'),
(3, 2, 'Dionysus GG', 'Leather', 'Beige', 2200.00, 8, '2024-01-20'),
(4, 2, 'Marmont Matelassé', 'Leather', 'Pink', 1800.00, 12, '2024-02-10'),
(5, 3, 'Classic Flap', 'Leather', 'Black', 5000.00, 3, '2024-01-05'),
(6, 3, 'Boy Bag', 'Leather', 'Navy', 4500.00, 6, '2024-02-15'),
(7, 4, 'Birkin 35', 'Leather', 'Orange', 12000.00, 1, '2024-01-01'),
(8, 4, 'Kelly 28', 'Leather', 'Black', 10000.00, 2, '2024-01-30'),
(9, 5, 'Galleria Bag', 'Saffiano', 'Red', 2800.00, 7, '2024-02-05'),
(10, 5, 'Cahier Bag', 'Leather', 'White', 2400.00, 9, '2024-02-20'),
(11, 1, 'Elegant #2024', 'Leather', 'Brown', 1800.00, 15, '2024-03-01'),
(12, 2, 'Supreme #Collection', 'Canvas', 'Multi', 1600.00, 20, '2024-03-05');
